{"description": "Mock menu data for reference and testing purposes", "version": "1.0", "createdAt": "2025-04-26", "purpose": "This file contains the mock menu data structure used during development. It serves as a reference for the expected data format and can be used for testing.", "menus": [{"id": "1", "name": "Main Menu", "slug": "main-menu", "isActive": true, "isDefault": true, "description": "Our standard menu available most days", "categories": [{"id": "101", "name": "Appetizers", "slug": "appetizers", "categoryIngredients": [{"name": "<PERSON><PERSON><PERSON>", "category": "spice"}, {"name": "<PERSON><PERSON>", "category": "spice"}, {"name": "Spring Onion", "category": "vegetable"}], "categoryAddons": [{"name": "Extra Dipping Sauce", "price": 1.5, "description": "Additional serving of dipping sauce"}, {"name": "Side of Ranch", "price": 1.0, "description": "Creamy ranch dressing"}], "items": [{"id": "1001", "name": "Spring Rolls", "description": "Crispy vegetable spring rolls served with sweet chili sauce", "price": 8.5, "isAvailable": true}, {"id": "1002", "name": "<PERSON><PERSON><PERSON>", "description": "Freshly baked bread with garlic butter", "price": 6.0, "isAvailable": true}, {"id": "1003", "name": "Chicken Wings", "description": "Spicy buffalo wings served with blue cheese dip", "price": 12.5, "isAvailable": true, "hasSizes": true, "sizes": [{"name": "Small (6 pcs)", "adjustmentType": "absolute", "priceAdjustment": -2.5}, {"name": "Regular (10 pcs)", "adjustmentType": "absolute", "priceAdjustment": 0}, {"name": "Large (16 pcs)", "adjustmentType": "absolute", "priceAdjustment": 5.0}]}]}, {"id": "102", "name": "Main Courses", "slug": "main-courses", "categoryIngredients": [{"name": "Lettuce", "category": "vegetable"}, {"name": "Tomato", "category": "vegetable"}, {"name": "Onion", "category": "vegetable"}, {"name": "Cheese", "category": "dairy"}, {"name": "Pickles", "category": "vegetable"}], "categoryAddons": [{"name": "Extra Cheese", "price": 2.0, "description": "Additional serving of cheese"}, {"name": "<PERSON>", "price": 2.5, "description": "Crispy bacon strips"}, {"name": "Avocado", "price": 3.0, "description": "Fresh sliced avocado"}, {"name": "Fried Egg", "price": 1.5, "description": "Fried egg on top"}], "items": [{"id": "1004", "name": "<PERSON><PERSON> Burger", "description": "Juicy beef patty with lettuce, tomato, cheese, and special sauce", "price": 18.5, "isAvailable": true, "hasSizes": true, "sizes": [{"name": "Single Patty", "adjustmentType": "absolute", "priceAdjustment": 0}, {"name": "Double Patty", "adjustmentType": "absolute", "priceAdjustment": 5.0}], "hasVariations": true, "variations": [{"name": "Classic", "description": "Traditional beef burger", "adjustmentType": "absolute", "priceAdjustment": 0, "ingredients": ["beef patty", "lettuce", "tomato", "cheese", "special sauce"]}, {"name": "Cheeseburger Deluxe", "description": "With extra cheese and bacon", "adjustmentType": "absolute", "priceAdjustment": 2.0, "ingredients": ["beef patty", "lettuce", "tomato", "double cheese", "bacon", "special sauce"]}, {"name": "Veggie Option", "description": "Plant-based patty", "adjustmentType": "absolute", "priceAdjustment": 1.5, "ingredients": ["plant-based patty", "lettuce", "tomato", "vegan cheese", "special sauce"]}], "ingredients": [{"name": "<PERSON><PERSON>", "category": "protein"}, {"name": "Lettuce", "category": "vegetable"}, {"name": "Tomato", "category": "vegetable"}, {"name": "Cheese", "category": "dairy"}, {"name": "Special Sauce", "category": "condiment"}], "addons": [{"name": "Extra Patty", "price": 4.0, "description": "Add an additional beef patty"}, {"name": "<PERSON>", "price": 2.0, "description": "Add crispy bacon strips"}, {"name": "Fried Egg", "price": 1.5, "description": "Add a fried egg on top"}]}, {"id": "1005", "name": "Margherita Pizza", "description": "Classic pizza with tomato sauce, mozzarella, and fresh basil", "price": 16.0, "isAvailable": true, "hasSizes": true, "sizes": [{"name": "Medium (10\")", "adjustmentType": "absolute", "priceAdjustment": 0}, {"name": "Large (14\")", "adjustmentType": "absolute", "priceAdjustment": 4.0}, {"name": "Family (18\")", "adjustmentType": "absolute", "priceAdjustment": 8.0}], "hasVariations": true, "variations": [{"name": "Traditional", "description": "Classic thin crust", "adjustmentType": "absolute", "priceAdjustment": 0, "ingredients": ["flour", "tomato sauce", "mozzarella", "basil"]}, {"name": "Deep Dish", "description": "Thick crust with extra cheese", "adjustmentType": "absolute", "priceAdjustment": 2.5, "ingredients": ["flour", "tomato sauce", "extra mozzarella", "basil"]}, {"name": "Gluten Free", "description": "Gluten-free thin crust", "adjustmentType": "absolute", "priceAdjustment": 3.0, "ingredients": ["gluten-free flour", "tomato sauce", "mozzarella", "basil"]}]}, {"id": "1006", "name": "Fish and Chips", "description": "Beer-battered fish with chunky fries and tartar sauce", "price": 19.5, "isAvailable": true}]}, {"id": "103", "name": "Desserts", "slug": "desserts", "categoryIngredients": [{"name": "Nuts", "category": "allergen"}, {"name": "Whip<PERSON>", "category": "dairy"}], "categoryAddons": [{"name": "Extra Ice Cream", "price": 2.0, "description": "Additional scoop of vanilla ice cream"}, {"name": "Chocolate Sauce", "price": 1.0, "description": "Rich chocolate sauce drizzle"}, {"name": "<PERSON><PERSON>", "price": 1.0, "description": "Sweet caramel sauce drizzle"}, {"name": "Fresh Berries", "price": 2.5, "description": "Assortment of fresh seasonal berries"}], "items": [{"id": "1007", "name": "Chocolate Brownie", "description": "Warm chocolate brownie served with vanilla ice cream", "price": 9.5, "isAvailable": true, "hasSizes": true, "sizes": [{"name": "Individual", "adjustmentType": "absolute", "priceAdjustment": 0}, {"name": "Sharing (2 people)", "adjustmentType": "absolute", "priceAdjustment": 5.0}, {"name": "Family (4 people)", "adjustmentType": "absolute", "priceAdjustment": 12.0}], "ingredients": [{"name": "Chocolate", "category": "sweet"}, {"name": "Nuts", "category": "allergen"}, {"name": "Vanilla Ice Cream", "category": "dairy"}]}, {"id": "1008", "name": "Cheesecake", "description": "New York style cheesecake with berry compote", "price": 8.5, "isAvailable": true}]}]}, {"id": "2", "name": "Lunch Special", "slug": "lunch-special", "isActive": true, "isDefault": false, "description": "Special lunch menu with discounted prices", "availabilityStartTime": "11:00", "availabilityEndTime": "15:00", "availabilityDays": ["monday", "tuesday", "wednesday", "thursday", "friday"], "categories": [{"id": "201", "name": "Lunch Combos", "slug": "lunch-combos", "categoryIngredients": [{"name": "Lettuce", "category": "vegetable"}, {"name": "Tomato", "category": "vegetable"}, {"name": "Onion", "category": "vegetable"}, {"name": "Cheese", "category": "dairy"}], "categoryAddons": [{"name": "Upgrade to Large Fries", "price": 1.5, "description": "Upgrade your side to large fries"}, {"name": "Upgrade to Large Drink", "price": 1.0, "description": "Upgrade your drink to a large"}, {"name": "<PERSON><PERSON>t", "price": 3.5, "description": "Add a small dessert to your combo"}], "items": [{"id": "2001", "name": "Chicken Sandwich Combo", "description": "Grilled chicken sandwich with fries and a soft drink", "price": 12.99, "isAvailable": true, "specialOffer": true, "hasVariations": true, "variations": [{"name": "Grilled Chicken", "description": "Classic grilled chicken sandwich", "adjustmentType": "absolute", "priceAdjustment": 0, "ingredients": ["grilled chicken", "lettuce", "tomato", "mayo"]}, {"name": "Crispy Chicken", "description": "Breaded and fried chicken sandwich", "adjustmentType": "absolute", "priceAdjustment": 1.0, "ingredients": ["crispy chicken", "lettuce", "tomato", "mayo"]}, {"name": "Spicy Chicken", "description": "Spicy grilled chicken with jalapeños", "adjustmentType": "absolute", "priceAdjustment": 1.5, "ingredients": ["spicy chicken", "lettuce", "tomato", "spicy mayo", "jalapeños"]}], "ingredients": [{"name": "Chicken", "category": "protein"}, {"name": "Lettuce", "category": "vegetable"}, {"name": "Tomato", "category": "vegetable"}, {"name": "Mayo", "category": "condiment"}]}, {"id": "2002", "name": "<PERSON><PERSON> Lunch", "description": "Spaghetti bolognese with garlic bread and a small salad", "price": 14.5, "isAvailable": true, "specialOffer": true}, {"id": "2003", "name": "Soup & Salad", "description": "Soup of the day with a fresh garden salad", "price": 10.99, "isAvailable": true, "specialOffer": true}]}, {"id": "202", "name": "Sides", "slug": "sides", "items": [{"id": "2004", "name": "French Fries", "description": "Crispy golden fries with ketchup", "price": 4.5, "isAvailable": true}, {"id": "2005", "name": "Side Salad", "description": "Fresh garden salad with house dressing", "price": 5.0, "isAvailable": true}, {"id": "2006", "name": "Onion Rings", "description": "Crispy battered onion rings with aioli", "price": 5.5, "isAvailable": true}]}]}, {"id": "3", "name": "Weekend Menu", "slug": "weekend-menu", "isActive": false, "isDefault": false, "description": "Special menu for weekends with exclusive items", "categories": []}]}