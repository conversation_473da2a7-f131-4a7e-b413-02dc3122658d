// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #ff5722; // Orange - primary brand color
$secondary: #2e7d32; // Green - secondary brand color
$accent: #ff9800; // Light orange - accent color

$dark: #1d1d1d;
$dark-page: #121212;

// Custom colors for the takeaway platform
$success: #4caf50; // Green for success messages
$warning: #ffc107; // Amber for warnings
$negative: #f44336; // Red for errors
$info: #2196f3; // Blue for information

$positive: #21ba45;
$negative: #c10015;
$info: #31ccec;
$warning: #f2c037;
