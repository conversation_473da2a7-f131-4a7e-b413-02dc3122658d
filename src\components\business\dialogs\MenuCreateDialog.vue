<template>
  <q-dialog
    ref="dialogRef"
    persistent
    :maximized="$q.screen.lt.sm"
    transition-show="scale"
    transition-hide="scale"
    @hide="onDialogHide"
  >
    <q-card class="dialog-card">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ $t('business.menu.createMenu') }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-separator class="q-my-sm" />

      <q-card-section class="q-pt-none">
        <q-form @submit="onSubmit" class="q-gutter-md">
          <!-- Basic Information -->
          <div class="row q-col-gutter-md">
            <div class="col-12">
              <!-- Menu Name -->
              <q-input
                v-model="form.name"
                :label="$t('business.menu.menuName')"
                :rules="[
                  (val) =>
                    !!val ||
                    $t('validation.required', {
                      field: $t('business.menu.menuName'),
                    }),
                ]"
                outlined
                dense
                autofocus
              />
            </div>

            <div class="col-12">
              <!-- Menu Description -->
              <q-input
                v-model="form.description"
                :label="$t('business.menu.menuDescription')"
                type="textarea"
                rows="3"
                outlined
                dense
              />
            </div>
          </div>

          <!-- Menu Settings -->
          <q-separator class="q-my-md" />
          
          <div class="text-subtitle1 q-mb-md">{{ $t('business.menu.settings.title') }}</div>

          <div class="row q-col-gutter-md">
            <!-- Active Status -->
            <div class="col-12 col-md-6">
              <q-toggle
                v-model="form.isActive"
                :label="$t('business.menu.settings.visibility.active')"
                color="positive"
              />
              <div class="text-caption text-grey-7 q-mt-xs">
                {{ $t('business.menu.settings.visibility.activeDescription') }}
              </div>
            </div>

            <!-- Default Status -->
            <div class="col-12 col-md-6">
              <q-toggle
                v-model="form.isDefault"
                :label="$t('business.menu.settings.visibility.default')"
                color="primary"
                :disable="!form.isActive"
              />
              <div class="text-caption text-grey-7 q-mt-xs">
                {{ $t('business.menu.settings.visibility.defaultDescription') }}
              </div>
            </div>
          </div>

          <!-- Availability Settings -->
          <q-separator class="q-my-md" />
          
          <div class="text-subtitle1 q-mb-md">{{ $t('business.menu.settings.availability.title') }}</div>

          <div class="row q-col-gutter-md">
            <!-- Available All Day Toggle -->
            <div class="col-12">
              <q-toggle
                v-model="form.isAvailableAllDay"
                :label="$t('business.menu.settings.availability.allDay')"
                color="primary"
              />
            </div>

            <!-- Time Range (if not all day) -->
            <div v-if="!form.isAvailableAllDay" class="col-12">
              <div class="row q-col-gutter-md">
                <div class="col-6">
                  <q-input
                    v-model="form.availabilityStartTime"
                    :label="$t('business.menu.settings.availability.startTime')"
                    type="time"
                    outlined
                    dense
                  />
                </div>
                <div class="col-6">
                  <q-input
                    v-model="form.availabilityEndTime"
                    :label="$t('business.menu.settings.availability.endTime')"
                    type="time"
                    outlined
                    dense
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="row justify-end q-mt-md">
            <q-btn
              :label="$t('common.cancel')"
              color="grey-7"
              v-close-popup
              flat
              class="q-mr-sm"
            />
            <q-btn
              :label="$t('common.create')"
              color="primary"
              type="submit"
              :loading="loading"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useDialogPluginComponent } from "quasar";
import { createDebugger } from "src/utils/debug";

const debug = createDebugger("component:menu-create-dialog");
const { t } = useI18n();

// Dialog plugin setup
const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } =
  useDialogPluginComponent();

// Props
const props = defineProps({
  businessId: {
    type: String,
    required: true,
  },
});

// State
const loading = ref(false);
const form = ref({
  name: "",
  description: "",
  isActive: true,
  isDefault: false,
  isAvailableAllDay: true,
  availabilityStartTime: "",
  availabilityEndTime: "",
});

// Computed
const title = computed(() => t("business.menu.createMenu"));

// Watch for active status changes
watch(() => form.value.isActive, (newValue) => {
  if (!newValue) {
    form.value.isDefault = false;
  }
});

// Methods
function onSubmit() {
  debug("Submitting form", form.value);
  loading.value = true;

  // Generate slug from name
  const slug = form.value.name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-|-$/g, "");

  const menuData = {
    ...form.value,
    slug,
    businessId: props.businessId,
    displayOrder: 0, // Will be set by the backend
  };

  // Return the form data to the parent component
  onDialogOK(menuData);

  // Reset loading state (in case dialog is not closed)
  setTimeout(() => {
    loading.value = false;
  }, 500);
}
</script>

<style lang="scss" scoped>
.dialog-card {
  min-width: 400px;
  max-width: 600px;
  width: 90vw;
}
</style>
