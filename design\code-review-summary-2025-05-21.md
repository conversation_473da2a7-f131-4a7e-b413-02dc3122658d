# Code Review Summary - 2025-05-21

This document summarizes the findings from the code review conducted on May 21, 2025, based on the checklist in [`design/code-review-2025-05-21.md`](design/code-review-2025-05-21.md).

## Overall Stability Assessment Summary

The core data structures and store logic for the new referential menu system (ingredients, sizes, units, addons, and item ingredient/addon diffs) are largely well-implemented in the `MenuStore` ([`src/stores/menu.js`](src/stores/menu.js:1)) and `BusinessStore` ([`src/stores/business.js`](src/stores/business.js:1)). ID generation, validation, and resolver functions in the `MenuStore` for handling missing references are in place. The schema documentation ([`design/schemas/menu_schemas.md`](design/schemas/menu_schemas.md:1)) and translation files ([`src/i18n/en-US/index.js`](src/i18n/en-US/index.js:1)) have been updated accordingly. The option definition cards ([`IngredientsCard.vue`](src/components/business/menu/IngredientsCard.vue:1), [`SizeOptionsCard.vue`](src/components/business/menu/SizeOptionsCard.vue:1), [`UnitTypesCard.vue`](src/components/business/menu/UnitTypesCard.vue:1)) also correctly manage their respective entities with referential IDs.

However, there are **critical gaps and areas of concern regarding the UI implementation and data persistence flow, primarily within `MenuManagement.vue` and its dialogs (`MenuItemDialog.vue`, `MenuCategoryDialog.vue`)**:

### Key Issues & Areas for Immediate Action:

1.  **Incomplete `saveChanges` Functionality in `MenuManagement.vue`**:

    - The `saveChanges` method in [`MenuManagement.vue`](src/components/business/menu/MenuManagement.vue:669) is currently a mock and does not persist any of the local changes made to categories or items back to the `MenuStore` or backend. This is the most critical issue preventing full CRUD functionality.
    - This aligns with `TODO` comments found in [`src/pages/business/MenuPage.vue`](src/pages/business/MenuPage.vue:211) regarding this missing implementation.

2.  **Incorrect Ingredient Data Formatting in `MenuItemDialog.vue`**:

    - [`MenuItemDialog.vue`](src/components/business/dialogs/MenuItemDialog.vue:1) submits `form.ingredients` as an array of selected ingredient _objects_. The `MenuStore` (and subsequently `MenuManagement.vue` when preparing data for the store) expects this to be an `{ added: string[], removed: string[] }` diff object. This conversion logic is missing, meaning item ingredients won't be saved correctly.

3.  **Reliance on Mock Data / Incomplete Store Integration in `MenuManagement.vue`**:

    - [`MenuManagement.vue`](src/components/business/menu/MenuManagement.vue:1) is currently hardcoded to use `mockMenus` from the older, non-referential [`src/data/mockMenuData.js`](src/data/mockMenuData.js:1). It needs to be updated to use `mockReferentialMenus` and populate `businessStore.currentBusiness` with `mockBusinessReferenceData` from [`src/data/mockReferentialMenuData.js`](src/data/mockReferentialMenuData.js:1) to allow proper testing of the referential system.
    - Calls to actual `MenuStore` methods for fetching data (`fetchBusinessMenus`, `loadMenu`) are commented out.

4.  **Stubbed Functionality**:

    - Several actions in [`MenuManagement.vue`](src/components/business/menu/MenuManagement.vue:1) are stubbed (e.g., `deleteCategory`, `deleteMenuItem`, `duplicateMenuItem`).

5.  **Source of Truth for Business-Level Ingredients/Sizes/Units**:

    - When new ingredients are added via `IngredientsDialog` (triggered from `MenuCategoryDialog.vue` or `MenuItemDialog.vue`), they are pushed to a local/computed `businessIngredients` list within those dialogs. For data integrity, adding new global business-level options should ideally go through `BusinessStore` actions, which would then update the central list. The current approach might lead to inconsistencies.

6.  **Missing Item-Level Ingredient Display**:
    - [`MenuItem.vue`](src/components/business/menu/MenuItem.vue:1) does not display the item's effective ingredients. It doesn't utilize `menuStore.getEffectiveIngredients`.

### Minor Issues & Observations:

- **Console Logs**: Numerous `console.log` statements remain, especially in dialogs and menu option components. These should be reviewed and removed or conditionalized.
- **Linting/Formatting**: No explicit lint/format scripts in `package.json`. Assumed to be handled by Quasar's default tooling or IDE integrations.

### Recommendations:

1.  **Prioritize implementing `MenuManagement.vue->saveChanges()`**: This is essential for any menu modifications to be persisted. It needs to iterate through `currentMenu.value.categories` and `currentMenu.value.items` and call the appropriate `MenuStore` actions (e.g., `createCategory`, `updateCategory`, `createMenuItem`, `updateMenuItem`).
2.  **Fix Ingredient Formatting in `MenuManagement.vue`**: When handling submissions from `MenuItemDialog.vue`, `MenuManagement.vue` must convert the array of selected ingredient objects into the `{added, removed}` diff structure before calling `menuStore.createMenuItem/updateMenuItem`.
3.  **Update `MenuManagement.vue` to use Referential Mock Data**: Switch to `mockReferentialMenus` and correctly populate `businessStore` with `mockBusinessReferenceData` to enable meaningful testing of the referential system. Uncomment and integrate actual `MenuStore` calls for fetching data.
4.  **Implement Stubbed Functions**: Complete the `deleteCategory`, `deleteMenuItem`, and `duplicateMenuItem` functionalities.
5.  **Refactor New Ingredient/Option Handling**: Ensure that when a new ingredient, size, or unit is defined via the dialogs within `MenuCategoryDialog` or `MenuItemDialog` (or the option cards), the definition is saved to the `BusinessStore` first, making it part of the canonical list for the business.
6.  **Display Item Effective Ingredients**: Implement the display of an item's effective ingredients in `MenuItem.vue` using `menuStore.getEffectiveIngredients`.
7.  **Clean Up Console Logs**: Remove unnecessary `console.log` statements.

The foundational store logic for the referential system is mostly in place, but the UI layer, particularly `MenuManagement.vue`, requires significant work to correctly interact with this new system and persist changes. The application is not in a fully stable or feature-complete state regarding the new menu management capabilities due to these UI-level implementation gaps.
