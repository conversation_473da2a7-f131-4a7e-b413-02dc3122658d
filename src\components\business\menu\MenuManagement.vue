<template>
  <div class="menu-management">
    <!-- Main content with drawer layout -->
    <q-layout view="hHh LpR fFf" container class="menu-management-layout">
      <!-- Main content area -->
      <q-page-container>
        <q-page padding>
          <!-- Loading state -->
          <div v-if="loading" class="text-center q-pa-lg">
            <q-spinner color="primary" size="3rem" />
            <p class="text-subtitle1 q-mt-sm">Loading menu data...</p>
          </div>

          <!-- Error state -->
          <div v-else-if="error" class="text-center q-pa-lg">
            <q-icon name="error" size="3rem" color="negative" />
            <p class="text-subtitle1 q-mt-sm text-negative">{{ error }}</p>
            <q-btn
              color="primary"
              label="Retry"
              @click="fetchBusinessMenus"
              class="q-mt-md"
            />
          </div>

          <!-- Content state -->
          <div v-else>
            <!-- Action buttons -->
            <div class="row items-end q-mb-md">
              <div class="col">
                <h5 class="q-mt-none q-mb-xs" v-if="currentMenu">
                  {{ currentMenu.name }}
                </h5>
              </div>
              <div class="col-auto">
                <q-btn
                  color="positive"
                  icon="save"
                  :label="$t('common.saveChanges')"
                  :disable="!hasChanges"
                  @click="saveChanges"
                  class="q-mr-sm"
                />
                <q-btn
                  :color="showSettings ? 'primary' : 'grey'"
                  :flat="!showSettings"
                  :outline="showSettings"
                  icon="settings"
                  :label="$t('business.menu.settings.toggle')"
                  @click="showSettings = !showSettings"
                  class="q-mr-sm"
                />
                <q-btn
                  flat
                  round
                  dense
                  icon="menu"
                  class="q-ml-sm"
                  @click="drawerOpen = !drawerOpen"
                >
                  <q-tooltip>{{ $t("business.menu.menuList") }}</q-tooltip>
                </q-btn>
              </div>
            </div>

            <!-- Menu Settings Card -->
            <MenuSettingsCard
              v-if="showSettings && currentMenu"
              :current-menu="currentMenu"
              @edit-availability="editMenuAvailability"
              @edit-visibility="editMenuVisibility"
              @update-menu="updateMenu"
              @export-menu="exportMenu"
              @import-menu="importMenu"
              class="menu-settings-card"
              :class="{ 'settings-card-enter-active': showSettings }"
            />

            <!-- Empty state -->
            <div class="text-center q-pa-lg" v-if="menuIsEmpty">
              <q-icon name="restaurant_menu" size="4rem" color="grey-5" />
              <p class="text-subtitle1 q-mt-sm">
                {{ $t("business.menu.empty") }}
              </p>
              <p class="text-caption q-mb-lg">
                {{ $t("business.menu.emptyPrompt") }}
              </p>

              <div class="row justify-center q-gutter-md">
                <q-btn
                  color="primary"
                  icon="add"
                  :label="$t('business.menu.addCategory')"
                  @click="addCategory"
                />
                <q-btn
                  color="secondary"
                  icon="add"
                  :label="$t('business.menu.addItem')"
                  @click="addMenuItem"
                />
              </div>
            </div>

            <!-- Menu content -->
            <div class="row q-col-gutter-md q-mt-md" v-else>
              <!-- Menu Categories Section -->
              <div class="col-12">
                <q-card class="menu-categories-card">
                  <q-card-section class="q-pb-none">
                    <div class="row items-center justify-between">
                      <div class="col">
                        <div class="text-h6">
                          {{ $t("business.menu.categories") }}
                        </div>
                        <p class="text-caption q-mt-sm">
                          {{ $t("business.menu.categoriesDescription") }}
                        </p>
                      </div>
                      <div class="col-auto">
                        <q-btn
                          color="primary"
                          icon="add"
                          :label="$t('business.menu.addCategory')"
                          @click="addCategory"
                        />
                      </div>
                    </div>
                  </q-card-section>

                  <q-separator class="q-mt-md" />

                  <q-card-section
                    v-if="
                      currentMenu &&
                      currentMenu.categories &&
                      currentMenu.categories.length > 0
                    "
                  >
                    <q-list separator>
                      <MenuCategory
                        v-for="category in currentMenu.categories"
                        :key="category.id"
                        :category="category"
                        @edit-category="editCategory"
                        @add-menu-item="addMenuItem"
                        @delete-category="deleteCategory"
                        @edit-item="editMenuItem"
                        @duplicate-item="duplicateMenuItem"
                        @delete-item="deleteMenuItem"
                      />
                    </q-list>
                  </q-card-section>

                  <q-card-section v-else class="text-center q-py-lg">
                    <q-icon name="category" size="3rem" color="grey-5" />
                    <p class="text-subtitle1 q-mt-sm">
                      {{ $t("business.menu.noCategories") }}
                    </p>
                    <p class="text-caption q-mb-md">
                      {{ $t("business.menu.noCategoriesPrompt") }}
                    </p>
                    <q-btn
                      color="primary"
                      icon="add"
                      :label="$t('business.menu.addCategory')"
                      @click="addCategory"
                    />
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </q-page>
      </q-page-container>

      <!-- Right-side drawer for menu list -->
      <q-drawer
        v-model="drawerOpen"
        side="right"
        bordered
        :width="300"
        :breakpoint="700"
      >
        <MenuDrawer
          :menus="menus"
          :current-menu="currentMenu"
          :loading="loading"
          :error="error"
          @select-menu="loadMenu"
          @create-menu="createMenu"
          @edit-menu-settings="editMenuSettings"
          @duplicate-menu="duplicateMenu"
          @delete-menu="deleteMenu"
          @refresh="fetchBusinessMenus"
        />
      </q-drawer>
    </q-layout>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { createDebugger } from "src/utils/debug";
import { useMenuStore } from "src/stores/menu";
import { useBusinessStore } from "src/stores/business";
import { useQuasar } from "quasar";
import MenuDrawer from "./MenuDrawer.vue";
import MenuSettingsCard from "./MenuSettingsCard.vue";
import MenuCategory from "./MenuCategory.vue";
import MenuItem from "./MenuItem.vue";
import MenuCategoryDialog from "../dialogs/MenuCategoryDialog.vue";
import MenuItemDialog from "../dialogs/MenuItemDialog.vue";
import MenuCreateDialog from "../dialogs/MenuCreateDialog.vue";
import MenuSettingsDialog from "../dialogs/MenuSettingsDialog.vue";
import MenuDeleteConfirmDialog from "../dialogs/MenuDeleteConfirmDialog.vue";

const debug = createDebugger("component:menu-management");
const { t } = useI18n();
const menuStore = useMenuStore();
const businessStore = useBusinessStore();
const $q = useQuasar();

// Props
const props = defineProps({
  businessId: {
    type: String,
    required: true,
  },
});

// Emits
const emit = defineEmits([
  "update",
  "change",
  "create-menu",
  "edit-menu-settings",
  "delete-menu",
]);

// State
const hasChanges = ref(false); // Track changes in the menu management
const drawerOpen = ref(true); // Control the right drawer - set to true to show drawer by default
const showSettings = ref(false); // Control the visibility of the settings card

// Use store state
const menus = computed(() => menuStore.menus);
const currentMenu = computed(() => menuStore.currentMenu);
const loading = computed(() => menuStore.loading);
const error = computed(() => menuStore.error);

// Computed properties
const menuIsEmpty = computed(() => {
  return (
    !menus.value ||
    menus.value.length === 0 ||
    (currentMenu.value &&
      (!currentMenu.value.categories ||
        currentMenu.value.categories.length === 0))
  );
});

// Computed property to check if any category has items
// Currently not used but will be useful for future features
// const hasMenuItems = computed(() => {
//   if (!currentMenu.value || !currentMenu.value.categories) return false;
//
//   // Check if any category has items
//   return currentMenu.value.categories.some((category) => {
//     return category.items && category.items.length > 0;
//   });
// });

// Watch for changes and emit events
watch(hasChanges, (newValue) => {
  debug("hasChanges changed:", newValue);
  emit("change", newValue);
});

// Computed properties for business ID
const actualBusinessId = computed(() => {
  // If we have a current business in the store, use its ID
  if (businessStore.currentBusiness) {
    return businessStore.currentBusiness.id;
  }

  // Otherwise, try to use the prop (which might be a slug)
  return props.businessId;
});

// Fetch menus on component mount
onMounted(async () => {
  // If the businessId prop is a slug, make sure we have the actual business ID first
  if (
    props.businessId &&
    !isUuid(props.businessId) &&
    !businessStore.currentBusiness
  ) {
    try {
      await businessStore.setCurrentBusiness(props.businessId);
    } catch (err) {
      debug("Error setting current business:", err);
      error.value = err.message;
      return;
    }
  }

  await fetchBusinessMenus();
});

// Methods
// Helper to check if a string is a UUID
function isUuid(str) {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

async function fetchBusinessMenus() {
  if (!actualBusinessId.value) {
    debug("No business ID available");
    return;
  }

  debug(`Fetching menus for business: ${actualBusinessId.value}`);

  try {
    // Fetch menus from the store using the actual business ID
    const fetchedMenus = await menuStore.fetchBusinessMenus(
      actualBusinessId.value
    );

    // If there are menus, set the first one as current
    if (fetchedMenus && fetchedMenus.length > 0) {
      await loadMenu(fetchedMenus[0].id);
    }
  } catch (err) {
    debug("Error fetching business menus:", err);
    // Error is already set in the store, no need to set it here
  }
}

async function loadMenu(menuId) {
  debug(`Loading menu: ${menuId}`);

  try {
    // Get the complete menu with categories and items using the actual business ID
    const menu = await menuStore.getMenu(menuId, actualBusinessId.value, true);
    return menu;
  } catch (err) {
    debug("Error loading menu:", err);
    // Error is already set in the store, no need to set it here
  }
}

function addCategory() {
  debug("Opening add category dialog");

  if (!currentMenu.value) {
    debug("No current menu selected");
    return;
  }

  $q.dialog({
    component: MenuCategoryDialog,
    componentProps: {
      isEdit: false,
    },
  }).onOk(async (newCategory) => {
    debug("New category created:", newCategory);

    try {
      // Add menuId to the category data
      newCategory.menuId = currentMenu.value.id;

      // Create the category using the store
      await menuStore.createCategory(newCategory);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.categoryCreated"),
        icon: "check_circle",
        position: "top",
        timeout: 2000,
      });
    } catch (err) {
      debug("Error creating category:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorCreatingCategory"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    }
  });
}

function editCategory(updatedCategory) {
  debug(`Editing category:`, updatedCategory);

  // If we received the category directly from the MenuCategory component
  if (updatedCategory && updatedCategory.id) {
    // This case is handled by the dialog onOk callback
    debug(
      "Category data received directly, this should be handled by dialog callback"
    );
    return;
  }
  // If we just received a category ID, open the dialog
  else if (typeof updatedCategory === "string") {
    const categoryId = updatedCategory;
    const categoryToEdit = currentMenu.value.categories.find(
      (c) => c.id === categoryId
    );

    if (categoryToEdit) {
      $q.dialog({
        component: MenuCategoryDialog,
        componentProps: {
          category: categoryToEdit,
          isEdit: true,
        },
      }).onOk(async (updatedCategoryData) => {
        debug("Category updated:", updatedCategoryData);

        try {
          // Update the category using the store
          await menuStore.updateCategory(categoryId, updatedCategoryData);

          // Show success notification
          $q.notify({
            type: "positive",
            message: t("business.menu.categoryUpdated"),
            icon: "check_circle",
            position: "top",
            timeout: 2000,
          });
        } catch (err) {
          debug("Error updating category:", err);
          $q.notify({
            type: "negative",
            message: t("business.menu.errorUpdatingCategory"),
            icon: "error",
            position: "top",
            timeout: 3000,
          });
        }
      });
    }
  }
}

function deleteCategory(categoryId) {
  debug(`Deleting category: ${categoryId}`);

  if (!currentMenu.value || !currentMenu.value.categories) {
    debug("No current menu or categories found");
    return;
  }

  const categoryToDelete = currentMenu.value.categories.find(
    (c) => c.id === categoryId
  );
  if (!categoryToDelete) {
    debug(`Category ${categoryId} not found`);
    return;
  }

  const itemCount = categoryToDelete.items ? categoryToDelete.items.length : 0;
  const message =
    itemCount > 0
      ? t("business.menu.deleteCategoryWithItemsConfirmation", {
          categoryName: categoryToDelete.name,
          itemCount,
        })
      : t("business.menu.deleteCategoryConfirmation", {
          categoryName: categoryToDelete.name,
        });

  // Show confirmation dialog
  $q.dialog({
    title: t("business.menu.deleteCategory"),
    message: message,
    cancel: true,
    persistent: true,
    color: "negative",
  }).onOk(async () => {
    try {
      // Delete the category using the store
      await menuStore.deleteCategory(categoryId);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.categoryDeleted"),
        icon: "check_circle",
        position: "top",
        timeout: 2000,
      });
    } catch (err) {
      debug("Error deleting category:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorDeletingCategory"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    }
  });
}

function addMenuItem(categoryId) {
  debug(`Adding menu item to category: ${categoryId}`);

  if (!currentMenu.value) {
    debug("No current menu selected");
    return;
  }

  $q.dialog({
    component: MenuItemDialog,
    componentProps: {
      categoryId: categoryId,
      isEdit: false,
    },
  }).onOk(async (newItem) => {
    debug("New menu item created:", newItem);

    try {
      // Add required fields for the item
      newItem.menuId = currentMenu.value.id;
      newItem.categoryId = categoryId;

      // Create the menu item using the store
      await menuStore.createMenuItem(newItem);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.menuItemCreated"),
        icon: "check_circle",
        position: "top",
        timeout: 2000,
      });
    } catch (err) {
      debug("Error creating menu item:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorCreatingMenuItem"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    }
  });
}

function editMenuItem(itemId, categoryId) {
  debug(`Editing menu item: ${itemId} in category: ${categoryId}`);

  // Find the category and item
  const category = currentMenu.value.categories.find(
    (c) => c.id === categoryId
  );
  if (!category || !category.items) {
    debug(`Category ${categoryId} not found or has no items`);
    return;
  }

  const item = category.items.find((i) => i.id === itemId);
  if (!item) {
    debug(`Item ${itemId} not found in category ${categoryId}`);
    return;
  }

  // Open the dialog with the item data
  $q.dialog({
    component: MenuItemDialog,
    componentProps: {
      item: item,
      categoryId: categoryId,
      isEdit: true,
    },
  }).onOk(async (updatedItem) => {
    debug("Menu item updated:", updatedItem);

    try {
      // Update the menu item using the store
      await menuStore.updateMenuItem(itemId, updatedItem);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.menuItemUpdated"),
        icon: "check_circle",
        position: "top",
        timeout: 2000,
      });
    } catch (err) {
      debug("Error updating menu item:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorUpdatingMenuItem"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    }
  });
}

function duplicateMenuItem(itemId, categoryId) {
  debug(`Duplicating menu item: ${itemId} in category: ${categoryId}`);

  // Find the category and item
  const category = currentMenu.value.categories.find(
    (c) => c.id === categoryId
  );
  if (!category || !category.items) {
    debug(`Category ${categoryId} not found or has no items`);
    return;
  }

  const itemToDuplicate = category.items.find((i) => i.id === itemId);
  if (!itemToDuplicate) {
    debug(`Item ${itemId} not found in category ${categoryId}`);
    return;
  }

  try {
    // Create a duplicate of the item
    const duplicatedItem = {
      ...itemToDuplicate,
      name: `${itemToDuplicate.name} (Copy)`,
      slug: `${itemToDuplicate.slug}-copy`,
      menuId: currentMenu.value.id,
      categoryId: categoryId,
    };

    // Remove the original ID so a new one will be generated
    delete duplicatedItem.id;

    // Create the duplicated item using the store
    await menuStore.createMenuItem(duplicatedItem);

    // Show success notification
    $q.notify({
      type: "positive",
      message: t("business.menu.menuItemDuplicated"),
      icon: "check_circle",
      position: "top",
      timeout: 2000,
    });
  } catch (err) {
    debug("Error duplicating menu item:", err);
    $q.notify({
      type: "negative",
      message: t("business.menu.errorDuplicatingMenuItem"),
      icon: "error",
      position: "top",
      timeout: 3000,
    });
  }
}

function deleteMenuItem(itemId, categoryId) {
  debug(`Deleting menu item: ${itemId} in category: ${categoryId}`);

  // Find the category and item
  const category = currentMenu.value.categories.find(
    (c) => c.id === categoryId
  );
  if (!category || !category.items) {
    debug(`Category ${categoryId} not found or has no items`);
    return;
  }

  const itemToDelete = category.items.find((i) => i.id === itemId);
  if (!itemToDelete) {
    debug(`Item ${itemId} not found in category ${categoryId}`);
    return;
  }

  // Show confirmation dialog
  $q.dialog({
    title: t("business.menu.deleteMenuItem"),
    message: t("business.menu.deleteMenuItemConfirmation", {
      itemName: itemToDelete.name,
    }),
    cancel: true,
    persistent: true,
    color: "negative",
  }).onOk(async () => {
    try {
      // Delete the menu item using the store
      await menuStore.deleteMenuItem(itemId);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.menuItemDeleted"),
        icon: "check_circle",
        position: "top",
        timeout: 2000,
      });
    } catch (err) {
      debug("Error deleting menu item:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorDeletingMenuItem"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    }
  });
}

function createMenu() {
  debug("Opening create menu dialog");

  $q.dialog({
    component: MenuCreateDialog,
    componentProps: {
      businessId: actualBusinessId.value,
    },
  }).onOk(async (newMenuData) => {
    debug("New menu created:", newMenuData);

    try {
      // Create the menu using the store
      const newMenu = await menuStore.createMenu(newMenuData);

      // Load the new menu
      await loadMenu(newMenu.id);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.menuCreated"),
        icon: "check_circle",
        position: "top",
        timeout: 2000,
      });

      emit("create-menu", newMenu);
    } catch (err) {
      debug("Error creating menu:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorCreatingMenu"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    }
  });
}

function editMenuSettings(menuId) {
  debug(`Opening edit settings dialog for menu: ${menuId}`);

  const menuToEdit = menus.value.find((m) => m.id === menuId);
  if (!menuToEdit) {
    debug(`Menu with ID ${menuId} not found`);
    return;
  }

  $q.dialog({
    component: MenuSettingsDialog,
    componentProps: {
      menu: menuToEdit,
    },
  }).onOk(async (updatedMenuData) => {
    debug("Menu settings updated:", updatedMenuData);

    try {
      // Update the menu using the store
      await menuStore.updateMenu(menuId, updatedMenuData);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.menuSettingsUpdated"),
        icon: "check_circle",
        position: "top",
        timeout: 2000,
      });

      emit("edit-menu-settings", updatedMenuData);
    } catch (err) {
      debug("Error updating menu settings:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorUpdatingMenuSettings"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    }
  });
}

async function duplicateMenu(menuId) {
  debug(`Duplicating menu: ${menuId}`);
  loading.value = true;
  error.value = null;

  try {
    // First get the menu to duplicate
    const menuToDuplicate = menus.value.find((m) => m.id === menuId);
    if (!menuToDuplicate) {
      throw new Error(`Menu with ID ${menuId} not found`);
    }

    // Create a new menu based on the existing one
    const newMenuData = {
      businessId: actualBusinessId.value,
      name: `${menuToDuplicate.name} (Copy)`,
      slug: `${menuToDuplicate.slug}-copy`,
      description: menuToDuplicate.description,
      isActive: false, // Set to inactive by default
      isDefault: false, // Never set a duplicate as default
      availabilityStartTime: menuToDuplicate.availabilityStartTime,
      availabilityEndTime: menuToDuplicate.availabilityEndTime,
      availabilityDays: menuToDuplicate.availabilityDays,
      displayOrder: menuToDuplicate.displayOrder + 1,
    };

    // Create the new menu
    const newMenu = await menuStore.createMenu(newMenuData);

    // Refresh the menu list
    await fetchBusinessMenus();

    // Load the new menu
    await loadMenu(newMenu.id);

    // Show success notification
    $q.notify({
      type: "positive",
      message: t("business.menu.menuDuplicated"),
      icon: "check_circle",
      position: "top",
      timeout: 2000,
    });
  } catch (err) {
    debug("Error duplicating menu:", err);
    $q.notify({
      type: "negative",
      message: t("business.menu.errorDuplicatingMenu"),
      icon: "error",
      position: "top",
      timeout: 3000,
    });
  } finally {
    loading.value = false;
  }
}

function deleteMenu(menuId) {
  debug(`Opening delete confirmation for menu: ${menuId}`);

  const menuToDelete = menus.value.find((m) => m.id === menuId);
  if (!menuToDelete) {
    debug(`Menu with ID ${menuId} not found`);
    return;
  }

  $q.dialog({
    component: MenuDeleteConfirmDialog,
    componentProps: {
      menu: menuToDelete,
    },
  }).onOk(async (confirmedMenuId) => {
    debug("Menu deletion confirmed:", confirmedMenuId);

    try {
      // Delete the menu using the store
      await menuStore.deleteMenu(confirmedMenuId);

      // If this was the current menu, select another one or clear
      if (currentMenu.value && currentMenu.value.id === confirmedMenuId) {
        if (menus.value.length > 0) {
          await loadMenu(menus.value[0].id);
        }
      }

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.menuDeleted"),
        icon: "check_circle",
        position: "top",
        timeout: 2000,
      });

      emit("delete-menu", confirmedMenuId);
    } catch (err) {
      debug("Error deleting menu:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorDeletingMenu"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    }
  });
}

// Menu Settings Card methods
function editMenuAvailability(menuId) {
  debug(`Editing availability for menu: ${menuId}`);
  // This will be implemented with a dialog component
  // For now, we'll just toggle the settings
  showSettings.value = true;
}

function editMenuVisibility(menuId) {
  debug(`Editing visibility for menu: ${menuId}`);
  // This will be implemented with a dialog component
  // For now, we'll just toggle the settings
  showSettings.value = true;
}

function updateMenu(menuId, updates) {
  debug(`Updating menu: ${menuId} with:`, updates);
  // For demo purposes, we'll update the menu in our mock data
  const menuIndex = menus.value.findIndex((m) => m.id === menuId);
  if (menuIndex !== -1) {
    menus.value[menuIndex] = { ...menus.value[menuIndex], ...updates };

    // If this is being set as the default menu, make sure no other menu is default
    if (updates.isDefault) {
      menus.value.forEach((menu, index) => {
        if (index !== menuIndex && menu.isDefault) {
          menus.value[index] = { ...menu, isDefault: false };
        }
      });
    }

    // Update current menu if it's the one being updated
    if (currentMenu.value && currentMenu.value.id === menuId) {
      currentMenu.value = { ...currentMenu.value, ...updates };
    }

    // Set hasChanges to true to track that we have unsaved changes
    hasChanges.value = true;
  }
}

function exportMenu(menuId) {
  debug(`Exporting menu: ${menuId}`);

  const menuToExport = menus.value.find((m) => m.id === menuId);
  if (!menuToExport) {
    debug(`Menu with ID ${menuId} not found`);
    return;
  }

  try {
    // Create a clean export object
    const exportData = {
      name: menuToExport.name,
      description: menuToExport.description,
      categories: menuToExport.categories || [],
      exportedAt: new Date().toISOString(),
      version: "1.0",
    };

    // Convert to JSON string
    const jsonString = JSON.stringify(exportData, null, 2);

    // Create and download file
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${menuToExport.name
      .replace(/[^a-z0-9]/gi, "_")
      .toLowerCase()}_menu.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    // Show success notification
    $q.notify({
      type: "positive",
      message: t("business.menu.menuExported"),
      icon: "download",
      position: "top",
      timeout: 2000,
    });
  } catch (err) {
    debug("Error exporting menu:", err);
    $q.notify({
      type: "negative",
      message: t("business.menu.errorExportingMenu"),
      icon: "error",
      position: "top",
      timeout: 3000,
    });
  }
}

function importMenu() {
  debug("Importing menu");

  // Create file input element
  const input = document.createElement("input");
  input.type = "file";
  input.accept = ".json";

  input.onchange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      loading.value = true;

      // Read file content
      const text = await file.text();
      const importData = JSON.parse(text);

      // Validate import data structure
      if (!importData.name || !Array.isArray(importData.categories)) {
        throw new Error("Invalid menu file format");
      }

      // Create new menu from imported data
      const newMenuData = {
        businessId: actualBusinessId.value,
        name: `${importData.name} (Imported)`,
        description: importData.description || "",
        isActive: false, // Set to inactive by default
        isDefault: false,
      };

      // Create the menu using the store
      const newMenu = await menuStore.createMenu(newMenuData);

      // Import categories and items
      if (importData.categories && importData.categories.length > 0) {
        for (const categoryData of importData.categories) {
          const newCategory = await menuStore.createCategory({
            ...categoryData,
            menuId: newMenu.id,
          });

          // Import items for this category
          if (categoryData.items && categoryData.items.length > 0) {
            for (const itemData of categoryData.items) {
              await menuStore.createMenuItem({
                ...itemData,
                menuId: newMenu.id,
                categoryId: newCategory.id,
              });
            }
          }
        }
      }

      // Refresh the menu list
      await fetchBusinessMenus();

      // Load the imported menu
      await loadMenu(newMenu.id);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.menuImported"),
        icon: "upload",
        position: "top",
        timeout: 2000,
      });
    } catch (err) {
      debug("Error importing menu:", err);
      $q.notify({
        type: "negative",
        message: t("business.menu.errorImportingMenu"),
        icon: "error",
        position: "top",
        timeout: 3000,
      });
    } finally {
      loading.value = false;
    }
  };

  // Trigger file selection
  input.click();
}

// Note: saveChanges function removed as we now use real-time updates with the store
// All changes are automatically saved when they are made
</script>

<style lang="scss" scoped>
.menu-management {
  width: 100%;

  .menu-management-layout {
    height: calc(100vh - 200px);
    min-height: 500px;
    width: 100%;
    border-radius: 4px;
  }

  .menu-categories-card,
  .menu-items-card {
    margin-bottom: 1.5rem;
  }

  .category-item {
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.03);
    }

    .category-header {
      padding: 12px 16px;
    }
  }

  .menu-items-list {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .q-btn-group {
    opacity: 0.7;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }
  }

  .menu-settings-card {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-in-out;

    &.settings-card-enter-active {
      max-height: 1000px;
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>
