{"name": "takeaway", "version": "0.0.1", "description": "Takeaway", "productName": "TakeawayNZ", "author": "<PERSON> <<EMAIL>>", "type": "module", "private": true, "scripts": {"test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "postinstall": "quasar prepare"}, "dependencies": {"@here/maps-api-for-javascript": "^1.64.0", "@quasar/extras": "^1.16.4", "@supabase/supabase-js": "^2.49.4", "axios": "^1.2.1", "debug": "^4.4.0", "marked": "^15.0.8", "pinia": "^3.0.1", "quasar": "^2.16.0", "short-unique-id": "^5.2.2", "vue": "^3.4.18", "vue-i18n": "^11.0.0", "vue-router": "^4.0.0"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^4.0.0", "@quasar/app-vite": "^2.1.0", "autoprefixer": "^10.4.2", "postcss": "^8.4.14"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}