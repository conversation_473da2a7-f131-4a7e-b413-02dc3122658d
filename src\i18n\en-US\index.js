// English (US) translations

export default {
  // Common translations used across the app
  common: {
    appName: "TakeawayNZ",
    loading: "Loading...",
    error: "An error occurred",
    success: "Success!",
    retry: "Retry",
    noResults: "No results found",
    save: "Save",
    saveChanges: "Save Changes",
    cancel: "Cancel",
    create: "Create",
    delete: "Delete",
    edit: "Edit",
    view: "View",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    back: "Back",
    next: "Next",
    previous: "Previous",
    submit: "Submit",
    continue: "Continue",
    confirm: "Confirm",
    yes: "Yes",
    no: "No",
    close: "Close",
    open: "Open",
    all: "All",
    none: "None",
    required: "Required",
    optional: "Optional",
    more: "More",
    less: "Less",
    language: "Language",
    copyright: "© {year} TakeawayNZ. All rights reserved.",
    remove: "Remove",
    add: "Add",
    apply: "Apply",
    days: {
      monday: {
        full: "Monday",
        short: "Mon",
      },
      tuesday: {
        full: "Tuesday",
        short: "Tue",
      },
      wednesday: {
        full: "Wednesday",
        short: "Wed",
      },
      thursday: {
        full: "Thursday",
        short: "Thu",
      },
      friday: {
        full: "Friday",
        short: "Fri",
      },
      saturday: {
        full: "Saturday",
        short: "Sat",
      },
      sunday: {
        full: "Sunday",
        short: "Sun",
      },
    },
  },

  // Navigation
  nav: {
    home: "Home",
    browse: "Browse",
    about: "About",
    contact: "Contact Us",
    login: "Login",
    register: "Register",
    logout: "Logout",
    profile: "Profile",
    orders: "Orders",
    menu: "Menu",
    dashboard: "Dashboard",
    settings: "Settings",
    help: "Help",
  },

  // Home page
  home: {
    hero: {
      title: "Order Takeaways Online",
      subtitle:
        "Browse menus from local takeaway shops and place your order for collection.",
      cta: "Find Takeaway",
    },
    features: {
      title: "Why Choose TakeawayNZ?",
      local: {
        title: "Support Local",
        description: "Help local businesses thrive by ordering directly.",
      },
      convenient: {
        title: "Convenient Ordering",
        description:
          "Order from anywhere, anytime with our easy-to-use platform.",
      },
      variety: {
        title: "Great Variety",
        description: "Discover a wide range of cuisines and dishes.",
      },
    },
  },

  // About page
  about: {
    title: "About TakeawayNZ",
    intro:
      "TakeawayNZ is a platform that connects customers with local takeaway shops across New Zealand. Our mission is to help small businesses reach more customers and provide a convenient ordering experience for food lovers.",
    story: {
      title: "Our Story",
      content:
        "Founded in 2025, TakeawayNZ was created to support local food businesses and make it easier for customers to discover and order from their favorite takeaway shops. We believe in supporting local businesses and providing a platform that makes online ordering accessible to all.",
    },
    howItWorks: {
      title: "How It Works",
      find: {
        title: "Find",
        description: "Discover local takeaway shops in your area.",
      },
      order: {
        title: "Order",
        description: "Browse menus and place your order online.",
      },
      collect: {
        title: "Collect",
        description: "Pick up your order at the specified time.",
      },
    },
    contact: {
      title: "Contact Us",
      prompt: "Have questions or feedback? We'd love to hear from you!",
      button: "Contact Us",
    },
  },

  // Contact page
  contact: {
    title: "Contact Us",
    subtitle:
      "We'd love to hear from you! Send us a message and we'll get back to you as soon as possible.",
    form: {
      name: "Name",
      email: "Email",
      subject: "Subject",
      message: "Message",
      submit: "Send Message",
    },
    info: {
      title: "Contact Information",
      email: "<EMAIL>",
      phone: "+64 9 123 4567",
      address: "123 Queen Street, Auckland, New Zealand",
      hours: {
        title: "Business Hours",
        weekdays: "Monday - Friday: 9:00 AM - 5:00 PM",
        weekend: "Saturday - Sunday: Closed",
      },
    },
    success:
      "Your message has been sent successfully. We'll get back to you soon!",
    error: "There was an error sending your message. Please try again.",
  },

  // Authentication
  auth: {
    login: {
      title: "Login",
      subtitle: "Welcome back to TakeawayNZ",
      email: "Email",
      password: "Password",
      remember: "Remember me",
      forgot: "Forgot password?",
      submit: "Login",
      noAccount: "Don't have an account?",
      register: "Register now",
      success: "Login successful",
    },
    register: {
      title: "Create an Account",
      subtitle: "Join TakeawayNZ to order from local takeaway shops",
      firstName: "First Name",
      lastName: "Last Name",
      username: "Username",
      email: "Email",
      phone: "Phone Number",
      password: "Password",
      confirmPassword: "Confirm Password",
      terms: "I agree to the Terms and Conditions",
      termsRequired: "You must agree to the terms",
      submit: "Register",
      hasAccount: "Already have an account?",
      login: "Login now",
      success: "Registration successful",
      emailConfirmationSent:
        "A confirmation email has been sent to your email address. Please check your inbox and follow the instructions to verify your account.",
    },
    forgot: {
      title: "Forgot Password",
      subtitle: "Enter your email to reset your password",
      email: "Email",
      submit: "Reset Password",
      back: "Back to Login",
      remembered: "Remembered your password?",
      resetSent: "Password reset instructions have been sent to your email.",
    },
    reset: {
      title: "Reset Password",
      subtitle: "Enter your new password",
      password: "New Password",
      confirmPassword: "Confirm New Password",
      submit: "Reset Password",
      success: "Your password has been reset successfully.",
    },
    errors: {
      title: "Authentication Error",
      invalidCredentials: "Invalid email or password",
      emailExists: "Email already exists",
      passwordMismatch: "Passwords do not match",
      weakPassword: "Password is too weak",
      emailNotFound: "Email not found",
      server_error: "Server error occurred. Please try again later.",
      unexpected_failure:
        "An unexpected error occurred. Please try again later.",
      generic: "Authentication failed. Please try again.",
      tryAgain: "Try Again",
      home: "Go to Homepage",
    },
    verification: {
      title: "Verify Your Email",
      subtitle: "Please check your email to complete registration",
      instructions: "We've sent a verification email to:",
      resend: "Resend Email",
      login: "Back to Login",
      cooldown: "You can request another email in {seconds} seconds",
      resendSuccess: "Verification email has been resent",
      emailRequired: "Email address is required",
      success: "Email verified successfully",
      error: "Invalid verification link",
    },
  },

  // Business pages
  business: {
    selector: {
      label: "Select Business",
      switchBusiness: "Switch Business",
    },
    navigation: {
      title: "Business Management",
      menuManagement: "Menu Management",
      businessHours: "Business Hours",
      staffManagement: "Staff Management",
      viewPublicSite: "View Public Site",
      switchBusiness: "Switch Business",
    },
    select: {
      title: "Select a Business",
      description: "Choose a business to manage or register a new one.",
      noBusinesses: "You don't have any businesses yet.",
      registerBusiness: "Register a Business",
      registerAnother: "Register Another Business",
    },
    noBusinessSelected: "No business selected",
    selectBusiness: "Select Business",
    dashboard: {
      statusOpen: "Open",
      statusClosed: "Closed",
      manageSettings: "Manage Settings",
      viewOrders: "View Orders",
      editMenu: "Edit Menu",
      updateHours: "Update Hours",
      onlineOrdering: "Online Ordering",
      businessVerification: "Business Verification",
      verified: "Verified",
      pending: "Pending",
      menuItems: "Menu Items",
      staffMembers: "Staff Members",
      profileCompletion: "Profile Completion",
      currentStatus: "Current Status",
      onlineOrderingEnabled: "Online ordering has been enabled",
      onlineOrderingDisabled: "Online ordering has been disabled",
      nextOpenTime: "Opens {day} at {time}",
      closesAt: "Closes at {time}",
      closesIn: "Closes in {timeUntilClose}",
    },
    register: {
      title: "Register Your Business",
      subtitle:
        "Join TakeawayNZ to reach more customers and streamline your order process.",
    },
    onboarding: {
      success:
        "Business registration successful! Your business is now being reviewed.",
      steps: {
        details: {
          title: "Business Details",
          description:
            "Let's start with some basic information about your business.",
        },
        hours: {
          title: "Business Hours",
          description:
            "Set your regular business hours and any special exceptions.",
        },
        menu: {
          title: "Menu Setup",
          description:
            "Create your menu categories and add your first menu items.",
        },
        orderSettings: {
          title: "Order Settings",
          description: "Configure how you want to handle online orders.",
        },
        review: {
          title: "Review & Preview",
          description: "Review your business profile before proceeding.",
        },
        notifications: {
          title: "Notification Setup",
          description: "Choose how you want to be notified about new orders.",
        },
        verification: {
          title: "Verification Request",
          description:
            "Submit your business for verification to start accepting orders.",
        },
        terms: {
          title: "Terms & Conditions",
          description:
            "Please review and accept our terms and conditions to complete your registration.",
        },
      },
    },
    fields: {
      name: "Business/Trading Name",
      slug: "Business URL Handle",
      slugHint:
        "This will be used in your business URL (e.g., takeaway.nz/your-name)",
      selectSlug: "Select a URL for your business",
      customSlug: "Or enter a custom URL",
      previewUrl: "takeaway.nz/{slug}",
      streetAddress: "Street Address",
      suburb: "Suburb",
      city: "City",
      postCode: "Post Code",
      address: "Address",
      searchAddress: "Search for an address",
      manualAddressInput: "Manual Address Input",
      phone: "Business Phone",
      email: "Business Email",
      contact: "Contact Information",
      gstNumber: "GST Number",
      gstNumberHint: "Optional, but recommended for tax purposes",
      cuisineTypes: "Cuisine Types",
      description: "Business Description",
      logo: "Business Logo",
      logoUpload: "Upload Logo",
      addCuisine: "Add Cuisine Type",
      selectCuisines: "Select Cuisine Types",
    },
    validation: {
      nameRequired: "Business name is required",
      slugRequired: "URL handle is required",
      slugFormat: "Only lowercase letters, numbers, and hyphens are allowed",
      slugTaken: "This URL handle is already taken",
      noSlugsAvailable:
        "No available URL handles found. Please try a custom one.",
      selectValidSlug: "Please select a valid and available URL handle",
      streetAddressRequired: "Street address is required",
      suburbRequired: "Suburb is required",
      cityRequired: "City is required",
      postCodeRequired: "Post code is required",
      postCodeFormat: "Post code must be a valid New Zealand post code",
      phoneRequired: "Business phone is required",
      emailRequired: "Business email is required",
      emailFormat: "Please enter a valid email address",
      cuisineRequired: "Please select at least one cuisine type",
      descriptionRequired: "Business description is required",
      slugCheck: "Error checking handle availability",
      logoUpload: "Error uploading logo",
      endDateAfterStart: "End date must be after start date",
      prepTimeRequired: "Preparation time is required",
      prepTimeRange: "Preparation time must be between 5 and 120 minutes",
      contactNameRequired: "Contact name is required",
      contactPhoneRequired: "Contact phone is required",
      termsRequired: "You must agree to the terms and conditions",
    },
    hours: {
      regularHours: "Regular Business Hours",
      exceptions: "Special Exceptions",
      exceptionsDescription:
        "Add exceptions for holidays, special events, or temporary closures.",
      noExceptions:
        "No exceptions added yet. Click the button below to add one.",
      addException: "Add Exception",
      date: "Date",
      dateUntil: "Until Date",
      multiDay: "Multi-day Exception",
      description: "Description (e.g., 'Holiday', 'Special Event')",
      closed: "Closed on this day",
      open: "Open",
      openTime: "Opening Time",
      closeTime: "Closing Time",
      setHours: "Set Business Hours",
      selectDays: "Select days to apply these hours to:",
      setTimeSlots: "Set time slots:",
      bulkEdit: "Edit Multiple Days",
      applyToSelected: "Apply to selected days",
    },
    errors: {
      generic: "An error occurred. Please try again.",
      slugCheck: "Unable to check handle availability",
      logoUpload: "Error uploading logo",
      addressSearch:
        "Error searching for address. Please try again or enter manually.",
    },
    review: {
      preview: "Business Preview",
      urlPreview: "Your business URL will be",
      confirmDetails: "Please confirm your business details",
    },
    cuisine: {
      noneSelected:
        "No cuisine types selected. Click the button below to add cuisine types.",
      addCustom: "Add custom cuisine type",
    },
    notifications: {
      title: "Notification Settings",
      email: "Email Notifications",
      emailDescription: "Receive order notifications via email",
      sms: "SMS Notifications",
      smsDescription: "Receive order notifications via text message",
      push: "Push Notifications",
      pushDescription: "Receive order notifications on your device",
    },
    verification: {
      title: "Business Verification",
      description:
        "Before your business can go live on TakeawayNZ, we need to verify your business information. Please provide contact details for verification.",
      contactName: "Contact Name",
      contactPhone: "Contact Phone",
      additionalNotes: "Additional Notes",
      additionalNotesHint:
        "Any additional information that might help with verification",
      termsAcceptance:
        "I confirm that all information provided is accurate and I agree to the TakeawayNZ Business Terms and Conditions",
    },
    terms: {
      loading: "Loading terms and conditions...",
      loadError: "Failed to load terms and conditions. Please try again later.",
      acceptanceLabel:
        "I have read and agree to the TakeawayNZ Business Terms and Conditions",
      acceptRequired: "You must accept the terms and conditions to continue",
    },
    menu: {
      title: "Menu Management",
      empty: "Your menu is empty",
      emptyPrompt: "Start by adding categories and menu items",
      addCategory: "Add Category",
      addItem: "Add Menu Item",
      addItemToCategory: "Add Item to Category",
      menus: "Menus",
      menuList: "Menu List",
      createMenu: "Create Menu",
      createFirstMenu: "Create Your First Menu",
      noMenus: "No menus found",
      editSettings: "Edit Settings",
      duplicate: "Duplicate Menu",
      default: "Default",
      active: "Active",
      inactive: "Inactive",
      categories: "Categories",
      categoriesDescription:
        "Organize your menu by creating categories for your items",
      noCategories: "No categories found",
      noCategoriesPrompt: "Start by adding a category to your menu",
      items: "Menu Items",
      itemsDescription: "Add items to your menu with prices and options",
      noItems: "No menu items found",
      noItemsPrompt: "Add items to your categories",
      itemsPlaceholder: "Menu items will appear here once added",
      availabilityEveryDay: "Every day",
      availabilityWeekdays: "Weekdays",
      availabilityWeekends: "Weekends",
      itemsCount: "items",
      specialOffer: "Special Offer",
      unavailable: "Unavailable",
      noCategoryItems: "No items in this category yet",
      categoryIngredients: "Category Ingredients",
      categoryAddons: "Category Add-ons",
      canBeRemoved: "Can be removed from items in this category",
      hasSizes: "Sizes",
      hasVariations: "Variations",
      sizeOptions: "Size Options",
      variationOptions: "Variation Options",
      basePrice: "Base Price",
      ingredients: "Ingredients",
      categoryName: "Category Name",
      categoryDescription: "Category Description",
      categoryIngredientsHint:
        "Ingredients that can be removed from items in this category",
      categoryAddonsHint: "Add-ons that can be added to items in this category",
      selectIngredients: "Select ingredients",
      noIngredientsFound: "No ingredients found",
      ingredientName: "Ingredient Name",
      ingredientCategory: "Category (e.g., vegetable, dairy)",
      addIngredient: "Add Ingredient",
      addNewIngredient: "Add New Ingredient",
      addNewIngredientMessage: "Enter the name of the new ingredient",
      selectCategory: "Select Category",
      selectCategoryForIngredient: "Select a category for {ingredient}",
      ingredientAdded: "Ingredient added successfully",
      ingredientUpdated: "Ingredient updated successfully",
      ingredientAlreadyExists: "This ingredient already exists",
      categories: {
        vegetable: "Vegetable",
        protein: "Protein",
        dairy: "Dairy",
        spice: "Spice",
        other: "Other",
      },
      addonName: "Add-on Name",
      addonPrice: "Price",
      addonDescription: "Description",
      addNewAddon: "Add New Add-on",
      addAddon: "Add Add-on",
      availabilitySettings: "Availability Settings",
      categoryAvailable: "Category is available",
      editCategory: "Edit Category",
      addCategory: "Add Category",
      editItem: "Edit Menu Item",
      addItem: "Add Menu Item",
      itemName: "Item Name",
      itemDescription: "Item Description",
      basePrice: "Base Price",
      itemSizes: "Item Sizes",
      itemSizesHint: "Add different size options for this item",
      selectSize: "Select Size",
      addSize: "Add Size",
      addNewSize: "Add a new size option",
      itemVariations: "Item Variations",
      itemVariationsHint:
        "Add different variations of this item (e.g., spicy, gluten-free)",
      variationName: "Variation Name",
      addVariation: "Add Variation",
      addNewVariation: "Add a new variation option",
      priceAdjustment: "Price Adjustment",
      itemIngredients: "Item Ingredients",
      itemIngredientsHint: "Ingredients that can be removed from this item",
      itemAvailable: "Item is available",
      changesSaved: "Changes saved successfully",
      errorSaving: "Error saving changes. Please try again.",
      menuName: "Menu Name",
      menuDescription: "Menu Description",
      editMenuSettings: "Edit Menu Settings",
      menuCreated: "Menu created successfully",
      errorCreatingMenu: "Error creating menu. Please try again.",
      menuSettingsUpdated: "Menu settings updated successfully",
      errorUpdatingMenuSettings:
        "Error updating menu settings. Please try again.",
      deleteMenu: "Delete Menu",
      deleteMenuConfirmation:
        "Are you sure you want to delete the menu '{menuName}'?",
      deleteMenuImpact: "This action will have the following impact:",
      deleteMenuCategoriesWarning:
        "{count} categories will be permanently deleted",
      deleteMenuItemsWarning: "{count} menu items will be permanently deleted",
      deleteMenuDefaultWarning:
        "This is your default menu - you'll need to set another menu as default",
      deleteMenuPermanentWarning: "This action cannot be undone",
      deleteMenuTypeConfirmation:
        "To confirm deletion, please type the menu name below:",
      typeMenuName: "Type menu name to confirm",
      menuNameMismatch: "Menu name does not match",
      menuDeleted: "Menu deleted successfully",
      errorDeletingMenu: "Error deleting menu. Please try again.",
      deleteCategory: "Delete Category",
      deleteCategoryConfirmation:
        "Are you sure you want to delete the category '{categoryName}'?",
      deleteCategoryWithItemsConfirmation:
        "Are you sure you want to delete the category '{categoryName}' and its {itemCount} items?",
      categoryDeleted: "Category deleted successfully",
      deleteMenuItem: "Delete Menu Item",
      deleteMenuItemConfirmation:
        "Are you sure you want to delete '{itemName}'?",
      menuItemDeleted: "Menu item deleted successfully",
      menuItemDuplicated: "Menu item duplicated successfully",
      menuExported: "Menu exported successfully",
      errorExportingMenu: "Error exporting menu. Please try again.",
      menuImported: "Menu imported successfully",
      errorImportingMenu:
        "Error importing menu. Please check the file format and try again.",
      menuDuplicated: "Menu duplicated successfully",
      errorDuplicatingMenu: "Error duplicating menu. Please try again.",
      categoryCreated: "Category created successfully",
      errorCreatingCategory: "Error creating category. Please try again.",
      categoryUpdated: "Category updated successfully",
      errorUpdatingCategory: "Error updating category. Please try again.",
      errorDeletingCategory: "Error deleting category. Please try again.",
      menuItemCreated: "Menu item created successfully",
      errorCreatingMenuItem: "Error creating menu item. Please try again.",
      menuItemUpdated: "Menu item updated successfully",
      errorUpdatingMenuItem: "Error updating menu item. Please try again.",
      errorDeletingMenuItem: "Error deleting menu item. Please try again.",
      errorDuplicatingMenuItem:
        "Error duplicating menu item. Please try again.",
      tabs: {
        menuManagement: "Menu Management",
        menuOptions: "Menu Options",
      },
      settings: {
        title: "Menu Settings",
        description: "Configure settings for this menu",
        toggle: "Settings",
        visibility: {
          title: "Menu Visibility",
          description: "Control which menu items are visible to customers",
          edit: "Edit Visibility",
          active: "Active",
          activeDescription: "Menu is visible to customers",
          default: "Default Menu",
          defaultDescription: "This menu will be shown by default to customers",
        },
        availability: {
          title: "Menu Availability",
          description: "Set when this menu is available",
          edit: "Edit Availability",
          notSet: "No availability settings configured",
          allDay: "Available all day",
          startTime: "Start Time",
          endTime: "End Time",
        },
        display: {
          title: "Display Settings",
          order: "Display Order",
          orderDescription: "Lower numbers appear first in the menu list",
        },
        importExport: {
          title: "Import/Export",
          description: "Import or export your menu data",
          import: "Import Menu",
          export: "Export Menu",
        },
      },
      options: {
        title: "Menu Options",
        description: "Configure options that can be applied to menu items",
        sizeOptions: {
          title: "Size Options",
          description: "Define size options for your menu items",
        },
        unitTypes: {
          title: "Unit Types",
          description: "Define unit types for measuring menu items",
        },
        ingredients: {
          title: "Ingredients",
          description: "Manage ingredients for customization",
        },
      },
    },
    orders: {
      title: "Orders",
      search: "Search orders...",
      dateRange: "Date Range",
      empty: "No orders found",
      emptyPrompt: "Orders will appear here when customers place them",
      acceptOnlineOrders: "Accept Online Orders",
      defaultPrepTime: "Default Preparation Time",
      minutes: "minutes",
      prepTimeDescription:
        "This is the average time it takes to prepare an order. Customers will be notified when to pick up their order based on this time.",
      status: {
        new: "New",
        preparing: "Preparing",
        ready: "Ready for Pickup",
        completed: "Completed",
        cancelled: "Cancelled",
      },
    },
  },

  // Customer pages
  customer: {
    browse: {
      title: "Browse Takeaway Shops",
      search: "Search for food or restaurants...",
      filter: "Filter",
      sort: "Sort by",
      empty: "No restaurants found",
      emptyPrompt: "Try adjusting your filters or search terms",
    },
    profile: {
      title: "My Profile",
      personalInfo: "Personal Information",
      name: "Name",
      email: "Email",
      phone: "Phone",
      address: "Address",
      password: "Password",
      changePassword: "Change Password",
      save: "Save Changes",
    },
    orders: {
      title: "My Orders",
      current: "Current Orders",
      past: "Past Orders",
      empty: "No orders found",
      emptyPrompt: "Your orders will appear here after you place them",
      details: "Order Details",
      status: {
        new: "Order Placed",
        preparing: "Preparing",
        ready: "Ready for Pickup",
        completed: "Completed",
        cancelled: "Cancelled",
      },
    },
  },

  // Validation messages
  validation: {
    required: "{field} is required",
    email: "Please enter a valid email address",
    minLength: "{field} must be at least {length} characters",
    maxLength: "{field} must not exceed {length} characters",
    numeric: "{field} must be a number",
    integer: "{field} must be an integer",
    alpha: "{field} must contain only letters",
    alphaNum: "{field} must contain only letters and numbers",
    sameAs: "{field} must match {other}",
    url: "Please enter a valid URL",
    date: "Please enter a valid date",
  },

  // Admin pages
  admin: {
    dashboard: {
      title: "Admin Dashboard",
      description: "Welcome to the TakeawayNZ admin dashboard.",
      businesses: "Businesses",
      users: "Users",
      development: "Development",
      viewAll: "View All",
    },
    businesses: {
      title: "Businesses",
      description: "Manage all businesses on the platform.",
      verify: "Verify",
      unverify: "Unverify",
      viewBusiness: "View Business",
      editBusiness: "Edit Business",
    },
    users: {
      title: "Users",
      description: "Manage all users on the platform.",
      viewUser: "View User",
      editUser: "Edit User",
      manageRoles: "Manage Roles",
      noRoles: "No roles",
    },
    devTools: {
      title: "Development Tools",
      description: "Tools for development and testing.",
      databaseReset: {
        title: "Database Reset",
        description:
          "Reset the database to a clean state for development and testing. This will delete all non-system data.",
        preservedTables: "Tables that will be preserved:",
        resetTables: "Tables that will be reset:",
        resetTablesNote:
          "Note: Tables that don't exist yet will be skipped automatically.",
        resetButton: "Reset Database",
        confirmTitle: "Confirm Database Reset",
        confirmMessage:
          "Are you sure you want to reset the database? This will delete all businesses, menus, orders, user profiles, and ALL Supabase user accounts (except your own). This action cannot be undone.",
        confirmButton: "Yes, Reset Database",
        success: "Database reset successful",
        error: "Error resetting database: {error}",
        lastResult: "Last Reset Result",
      },
    },
    errors: {
      notAuthenticated: "You must be logged in to access this page.",
      notAuthorized: "You do not have permission to access this page.",
      generic: "An error occurred. Please try again.",
    },
  },

  // Error pages
  error: {
    notFound: {
      title: "404 - Page Not Found",
      message: "The page you are looking for does not exist or has been moved.",
      button: "Go to Homepage",
    },
    serverError: {
      title: "500 - Server Error",
      message: "Something went wrong on our end. Please try again later.",
      button: "Refresh Page",
    },
    offline: {
      title: "You are offline",
      message: "Please check your internet connection and try again.",
      button: "Retry",
    },
  },
};
