<template>
  <q-dialog
    ref="dialogRef"
    persistent
    transition-show="scale"
    transition-hide="scale"
    @hide="onDialogHide"
  >
    <q-card class="dialog-card">
      <q-card-section class="row items-center q-pb-none">
        <q-icon name="warning" color="warning" size="md" class="q-mr-md" />
        <div class="text-h6">{{ $t('business.menu.deleteMenu') }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-separator class="q-my-sm" />

      <q-card-section class="q-pt-none">
        <!-- Warning Message -->
        <div class="text-body1 q-mb-md">
          {{ $t('business.menu.deleteMenuConfirmation', { menuName: menu.name }) }}
        </div>

        <!-- Menu Information -->
        <q-card flat bordered class="q-mb-md">
          <q-card-section>
            <div class="row items-center">
              <div class="col">
                <div class="text-weight-medium">{{ menu.name }}</div>
                <div class="text-caption text-grey-7" v-if="menu.description">
                  {{ menu.description }}
                </div>
              </div>
              <div class="col-auto">
                <q-badge
                  v-if="menu.isDefault"
                  color="primary"
                  text-color="white"
                  class="q-mr-xs"
                  outline
                >
                  {{ $t("business.menu.default") }}
                </q-badge>
                <q-badge
                  :color="menu.isActive ? 'positive' : 'grey'"
                  text-color="white"
                  outline
                >
                  {{ menu.isActive ? $t("business.menu.active") : $t("business.menu.inactive") }}
                </q-badge>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Impact Information -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm text-warning">
            {{ $t('business.menu.deleteMenuImpact') }}
          </div>
          <ul class="text-body2 text-grey-8">
            <li v-if="categoriesCount > 0">
              {{ $t('business.menu.deleteMenuCategoriesWarning', { count: categoriesCount }) }}
            </li>
            <li v-if="itemsCount > 0">
              {{ $t('business.menu.deleteMenuItemsWarning', { count: itemsCount }) }}
            </li>
            <li v-if="menu.isDefault">
              {{ $t('business.menu.deleteMenuDefaultWarning') }}
            </li>
            <li>{{ $t('business.menu.deleteMenuPermanentWarning') }}</li>
          </ul>
        </div>

        <!-- Confirmation Input -->
        <div class="q-mb-md">
          <div class="text-body2 q-mb-sm">
            {{ $t('business.menu.deleteMenuTypeConfirmation', { menuName: menu.name }) }}
          </div>
          <q-input
            v-model="confirmationText"
            :label="$t('business.menu.typeMenuName')"
            outlined
            dense
            autofocus
            :error="showError"
            :error-message="$t('business.menu.menuNameMismatch')"
          />
        </div>

        <!-- Action Buttons -->
        <div class="row justify-end q-mt-md">
          <q-btn
            :label="$t('common.cancel')"
            color="grey-7"
            v-close-popup
            flat
            class="q-mr-sm"
          />
          <q-btn
            :label="$t('common.delete')"
            color="negative"
            :disable="!isConfirmationValid"
            :loading="loading"
            @click="onConfirmDelete"
          />
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useDialogPluginComponent } from "quasar";
import { createDebugger } from "src/utils/debug";

const debug = createDebugger("component:menu-delete-confirm-dialog");
const { t } = useI18n();

// Dialog plugin setup
const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } =
  useDialogPluginComponent();

// Props
const props = defineProps({
  menu: {
    type: Object,
    required: true,
  },
});

// State
const loading = ref(false);
const confirmationText = ref("");
const showError = ref(false);

// Computed
const categoriesCount = computed(() => {
  return props.menu.categories ? props.menu.categories.length : 0;
});

const itemsCount = computed(() => {
  if (!props.menu.categories) return 0;
  return props.menu.categories.reduce((total, category) => {
    return total + (category.items ? category.items.length : 0);
  }, 0);
});

const isConfirmationValid = computed(() => {
  return confirmationText.value.trim() === props.menu.name.trim();
});

// Methods
function onConfirmDelete() {
  debug("Confirming menu deletion", props.menu.id);
  
  if (!isConfirmationValid.value) {
    showError.value = true;
    return;
  }

  loading.value = true;
  showError.value = false;

  // Return the menu ID to the parent component
  onDialogOK(props.menu.id);

  // Reset loading state (in case dialog is not closed)
  setTimeout(() => {
    loading.value = false;
  }, 500);
}
</script>

<style lang="scss" scoped>
.dialog-card {
  min-width: 400px;
  max-width: 500px;
  width: 90vw;
}
</style>
